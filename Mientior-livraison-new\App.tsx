import 'react-native-gesture-handler';
import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { PaperProvider } from 'react-native-paper';

import { useAuthStore } from './src/store/authStore';
import { useNotifications } from './src/hooks/useNotifications';
import AppNavigator from './src/navigation/AppNavigator';
import LoadingScreen from './src/screens/LoadingScreen';
import { colors } from './src/constants/theme';

// Configuration React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 2,
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (remplace cacheTime)
    },
    mutations: {
      retry: 1,
    },
  },
});

export default function App() {
  const notifications = useNotifications();

  // SOLUTION TEMPORAIRE: Désactiver complètement le LoadingScreen
  // pour déboguer le problème d'initialisation
  console.log('🚀 App.tsx rendu - pas de LoadingScreen');

  // if (loading) {
  //   return <LoadingScreen />;
  // }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <QueryClientProvider client={queryClient}>
          <PaperProvider
            theme={{
              colors: {
                primary: colors.primary[500],
                secondary: colors.secondary[500],
                surface: colors.surface.primary,
                background: colors.background.primary,
                error: colors.error,
                onSurface: colors.text.primary,
                onBackground: colors.text.primary,
              },
            }}
          >
            <NavigationContainer>
              <StatusBar style="dark" />
              <AppNavigator />
            </NavigationContainer>
          </PaperProvider>
        </QueryClientProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
