import React, { useEffect, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from '../store/authStore';
import { RootStackParamList } from '../types';

// Écrans d'authentification et onboarding
import LoadingScreen from '../screens/LoadingScreen';
import { OnboardingCarouselScreen } from '../screens/client/OnboardingCarouselScreen';
import LanguageSelectionScreen from '../screens/auth/LanguageSelectionScreen';
import LocationPermissionScreen from '../screens/auth/LocationPermissionScreen';
import { AuthChoiceScreen } from '../screens/client/AuthChoiceScreen';
import SignInScreen from '../screens/client/SignInScreen';
import SignUpScreen from '../screens/client/SignUpScreen';
import OTPVerificationScreen from '../screens/client/OTPVerificationScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import ResetPasswordScreen from '../screens/auth/ResetPasswordScreen';
import RoleSelectionScreen from '../screens/auth/RoleSelectionScreen';
import SupabaseTestScreen from '../screens/SupabaseTestScreen';

// Navigateurs par rôle
import ClientNavigator from './ClientNavigator';
import DeliveryNavigator from './DeliveryNavigator';
import MerchantNavigator from './MerchantNavigator';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const {
    user,
    isAuthenticated,
    initialize,
    loadUserAddresses,
    setCurrentLocation,
    setLocationPermission
  } = useAuthStore();
  const [isFirstTime, setIsFirstTime] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [initializationComplete, setInitializationComplete] = useState(false);
  const [minSplashTimeComplete, setMinSplashTimeComplete] = useState(false);

  // SOLUTION TEMPORAIRE: Forcer la fin du chargement après 5 secondes
  useEffect(() => {
    const forceEndLoading = setTimeout(() => {
      console.log('🚨 FORCE: Fin du chargement après 5 secondes');
      setInitializationComplete(true);
      setMinSplashTimeComplete(true);
      setIsFirstTime(true); // Montrer l'onboarding par défaut
      setIsLoading(false);
    }, 5000);

    return () => clearTimeout(forceEndLoading);
  }, []);

  // Délai minimum pour le SplashScreen (éviter le clignotement)
  useEffect(() => {
    const minSplashTimer = setTimeout(() => {
      setMinSplashTimeComplete(true);
    }, 2000); // 2 secondes minimum

    return () => clearTimeout(minSplashTimer);
  }, []);

  // Timeout de sécurité pour éviter le chargement infini
  useEffect(() => {
    const timeout = setTimeout(() => {
      console.log('⏰ Timeout de sécurité - forcer la fin du chargement');
      setInitializationComplete(true);
      if (isFirstTime === null) {
        setIsFirstTime(true); // Par défaut, montrer l'onboarding
      }
    }, 15000); // 15 secondes maximum

    return () => clearTimeout(timeout);
  }, [isFirstTime]);

  // Initialiser l'application et charger les données utilisateur
  useEffect(() => {
    let isMounted = true; // Éviter les mises à jour si le composant est démonté

    const initializeApp = async () => {
      try {
        console.log('🚀 Début initialisation app...');

        // Timeout pour éviter l'attente infinie
        const initPromise = initialize();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout initialisation')), 10000)
        );

        try {
          await Promise.race([initPromise, timeoutPromise]);
          console.log('✅ Initialisation auth terminée');
        } catch (initError) {
          console.error('⚠️ Erreur ou timeout initialisation auth:', initError);
        }

        // Vérifier si c'est la première fois
        const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
        if (isMounted) {
          setIsFirstTime(hasSeenOnboarding === null);
          console.log('📱 Premier lancement:', hasSeenOnboarding === null);
        }

        // Charger les données de localisation sauvegardées
        const locationPermission = await AsyncStorage.getItem('locationPermissionGranted');
        if (locationPermission && isMounted) {
          setLocationPermission(locationPermission === 'true');
          console.log('📍 Permission localisation:', locationPermission);
        }

        const lastLocation = await AsyncStorage.getItem('lastKnownLocation');
        if (lastLocation && isMounted) {
          try {
            const location = JSON.parse(lastLocation);
            setCurrentLocation(location);
            console.log('🗺️ Dernière position chargée');
          } catch (error) {
            console.error('Erreur parsing location:', error);
          }
        }

      } catch (error) {
        console.error('❌ Erreur initialisation app:', error);
        if (isMounted) {
          setIsFirstTime(true); // Par défaut, montrer l'onboarding
        }
      } finally {
        console.log('🏁 Fin initialisation app');
        if (isMounted) {
          setInitializationComplete(true);
        }
      }
    };

    initializeApp();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []);

  // Gérer la fin du chargement quand l'initialisation ET le délai minimum sont terminés
  useEffect(() => {
    if (initializationComplete && minSplashTimeComplete) {
      console.log('✅ Conditions remplies - fin du chargement');
      setIsLoading(false);
    }
  }, [initializationComplete, minSplashTimeComplete]);

  // Charger les adresses utilisateur quand l'utilisateur est authentifié
  useEffect(() => {
    if (isAuthenticated && user && user.role) {
      loadUserAddresses().catch(error => {
        console.error('Erreur chargement adresses:', error);
      });
    }
  }, [isAuthenticated, user?.id, user?.role, loadUserAddresses]);

  // Debug: Afficher l'état actuel
  console.log('🔍 État AppNavigator:', {
    isLoading,
    initializationComplete,
    minSplashTimeComplete,
    isFirstTime,
    isAuthenticated,
    user: user ? { id: user.id, role: user.role } : null
  });

  // Afficher un écran de chargement pendant la vérification
  // Attendre que l'initialisation ET le délai minimum soient terminés
  if (isLoading || !initializationComplete || !minSplashTimeComplete || isFirstTime === null) {
    console.log('📱 Affichage LoadingScreen - conditions non remplies');
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Loading" component={LoadingScreen} />
      </Stack.Navigator>
    );
  }

  // Si l'utilisateur n'est pas authentifié
  if (!isAuthenticated || !user) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
        initialRouteName={isFirstTime ? "Onboarding" : "AuthChoiceScreen"}
      >
        {/* Workflow d'onboarding complet */}
        <Stack.Screen name="Onboarding" component={OnboardingCarouselScreen} />
        <Stack.Screen name="LanguageSelection" component={LanguageSelectionScreen} />
        <Stack.Screen name="LocationPermission" component={LocationPermissionScreen} />

        {/* Écrans d'authentification */}
        <Stack.Screen name="AuthChoiceScreen" component={AuthChoiceScreen} />
        <Stack.Screen name="SignInScreen" component={SignInScreen} />
        <Stack.Screen name="SignUpScreen" component={SignUpScreen} />
        <Stack.Screen name="ForgotPasswordScreen" component={ForgotPasswordScreen} />
        <Stack.Screen name="ResetPasswordScreen" component={ResetPasswordScreen} />
        <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />

        {/* Écran de test */}
        <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
      </Stack.Navigator>
    );
  }

  // Si l'utilisateur est authentifié mais n'a pas de rôle défini
  if (!user.role) {
    return (
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
        }}
      >
        <Stack.Screen name="RoleSelection" component={RoleSelectionScreen} />
        <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
      </Stack.Navigator>
    );
  }

  // Navigateur selon le rôle utilisateur
  const getRoleNavigator = () => {
    switch (user.role) {
      case 'client':
        return ClientNavigator;
      case 'livreur':
        return DeliveryNavigator;
      case 'marchand':
        return MerchantNavigator;
      default:
        return ClientNavigator; // Par défaut, interface client
    }
  };

  const RoleNavigator = getRoleNavigator();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        gestureEnabled: false,
      }}
    >
      <Stack.Screen name="Main" component={RoleNavigator} />
      <Stack.Screen name="SupabaseTest" component={SupabaseTestScreen} />
    </Stack.Navigator>
  );
};

export default AppNavigator;
